import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, TextInput, TouchableOpacity, Alert, Image, Modal } from 'react-native';
import { Stack, router } from 'expo-router';
import { ArrowLeft, Camera, Check, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { Colors } from '@/constants/colors';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';
import { DatabaseService, UserProfile } from '@/services/database';
import { supabase } from '@/lib/supabase';
import { checkNetworkConnectivity, isNetworkError, getNetworkErrorMessage, retryWithBackoff } from '@/utils/network';
import { runSupabaseDiagnostics } from '@/utils/supabase-test';
import { ConfigDebug } from '@/components/debug/ConfigDebug';

export default function UpdateProfileScreen() {
  const { user } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [displayName, setDisplayName] = useState('');
  const [bio, setBio] = useState('');
  const [location, setLocation] = useState('');
  const [website, setWebsite] = useState('');
  const [avatarUri, setAvatarUri] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showConfigDebug, setShowConfigDebug] = useState(false);

  // Diagnostic function for troubleshooting
  const runDiagnostics = async () => {
    if (!user) return;

    console.log('Running Supabase diagnostics...');
    const diagnostics = await runSupabaseDiagnostics(user.id);

    console.log('Diagnostics results:', diagnostics);

    let message = 'Diagnostic Results:\n\n';
    message += `Connection Test: ${diagnostics.connectionTest.success ? 'PASSED' : 'FAILED'}\n`;

    if (!diagnostics.connectionTest.success) {
      message += `Connection Error: ${diagnostics.connectionTest.error}\n`;
    }

    if (diagnostics.profileTest) {
      message += `Profile Test: ${diagnostics.profileTest.success ? 'PASSED' : 'FAILED'}\n`;
      if (!diagnostics.profileTest.success) {
        message += `Profile Error: ${diagnostics.profileTest.error}\n`;
      }
    }

    if (diagnostics.recommendations.length > 0) {
      message += '\nRecommendations:\n';
      diagnostics.recommendations.forEach((rec, index) => {
        message += `${index + 1}. ${rec}\n`;
      });
    }

    Alert.alert('Diagnostics', message);
  };

  // Load user profile data
  useEffect(() => {
    const loadUserProfile = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        // Check network connectivity first
        const hasNetwork = await checkNetworkConnectivity();
        if (!hasNetwork) {
          Alert.alert(
            'No Internet Connection',
            'Please check your internet connection and try again.',
            [
              { text: 'Retry', onPress: () => loadUserProfile() },
              { text: 'Go Back', onPress: () => router.back() }
            ]
          );
          setIsLoading(false);
          return;
        }

        const profile = await retryWithBackoff(async () => {
          return await DatabaseService.getUserProfile(user.id);
        });

        if (profile) {
          setUserProfile(profile);
          setDisplayName(profile.display_name || '');
          setBio(profile.bio || '');
          setLocation(profile.location || '');
          setWebsite(profile.website_url || '');
          setAvatarUri(profile.avatar_url);
        } else {
          // No profile found - this might be a new user
          Alert.alert(
            'Profile Not Found',
            'No profile found for your account. Please go back and try again, or contact support if the issue persists.',
            [
              { text: 'Go Back', onPress: () => router.back() }
            ]
          );
        }
      } catch (error) {
        console.error('Error loading user profile:', error);
        const errorMessage = getNetworkErrorMessage(error);

        if (isNetworkError(error)) {
          Alert.alert(
            'Connection Error',
            errorMessage,
            [
              { text: 'Retry', onPress: () => loadUserProfile() },
              { text: 'Go Back', onPress: () => router.back() }
            ]
          );
        } else {
          Alert.alert('Error', errorMessage, [
            { text: 'Retry', onPress: () => loadUserProfile() },
            { text: 'Diagnostics', onPress: runDiagnostics },
            ...__DEV__ ? [{ text: 'Debug Config', onPress: () => setShowConfigDebug(true) }] : [],
            { text: 'Go Back', onPress: () => router.back() }
          ]);
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadUserProfile();
  }, [user]);

  const pickImage = async () => {
    try {
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera roll is required!');
        return;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setAvatarUri(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const uploadAvatar = async (uri: string): Promise<string | null> => {
    try {
      const fileExt = uri.split('.').pop();
      const fileName = `${user?.id}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      // Convert URI to blob for upload - handle both web and mobile
      let blob: Blob;

      if (uri.startsWith('file://') || uri.startsWith('content://')) {
        // Mobile: Use fetch with proper error handling
        try {
          const response = await fetch(uri);
          if (!response.ok) {
            throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
          }
          blob = await response.blob();
        } catch (fetchError) {
          console.error('Error fetching image from URI:', fetchError);
          throw new Error('Failed to read image file. Please try selecting a different image.');
        }
      } else {
        // Web or other platforms
        const response = await fetch(uri);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
        }
        blob = await response.blob();
      }

      // Validate blob
      if (!blob || blob.size === 0) {
        throw new Error('Invalid image file selected');
      }

      // Check file size (limit to 5MB)
      if (blob.size > 5 * 1024 * 1024) {
        throw new Error('Image file is too large. Please select an image smaller than 5MB.');
      }

      const { data, error } = await supabase.storage
        .from('user-content')
        .upload(filePath, blob, {
          contentType: `image/${fileExt}`,
          upsert: true,
        });

      if (error) {
        console.error('Supabase upload error:', error);
        throw new Error(`Upload failed: ${error.message}`);
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('user-content')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      // Re-throw with user-friendly message
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to upload avatar image');
    }
  };

  const handleSave = async () => {
    if (!user || !userProfile) return;

    // Validate required fields
    if (!displayName.trim()) {
      Alert.alert('Validation Error', 'Display name is required');
      return;
    }

    setIsSaving(true);
    try {
      let avatarUrl = userProfile.avatar_url;

      // Upload new avatar if selected
      if (avatarUri && avatarUri !== userProfile.avatar_url) {
        try {
          const uploadedUrl = await uploadAvatar(avatarUri);
          if (uploadedUrl) {
            avatarUrl = uploadedUrl;
          }
        } catch (uploadError) {
          console.error('Avatar upload failed:', uploadError);
          const errorMessage = uploadError instanceof Error ? uploadError.message : 'Unknown upload error';

          // Show error and ask user if they want to continue without avatar update
          Alert.alert(
            'Avatar Upload Failed',
            `${errorMessage}\n\nWould you like to save your other profile changes without updating the avatar?`,
            [
              { text: 'Cancel', style: 'cancel', onPress: () => setIsSaving(false) },
              { text: 'Save Without Avatar', onPress: () => continueWithProfileUpdate() }
            ]
          );
          return;
        }
      }

      await continueWithProfileUpdate(avatarUrl);
    } catch (error) {
      console.error('Error updating profile:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      Alert.alert('Error', `Failed to update profile: ${errorMessage}`);
      setIsSaving(false);
    }
  };

  const continueWithProfileUpdate = async (avatarUrl?: string) => {
    try {
      // Check network connectivity first
      const hasNetwork = await checkNetworkConnectivity();
      if (!hasNetwork) {
        Alert.alert(
          'No Internet Connection',
          'Please check your internet connection and try again.',
          [
            { text: 'Retry', onPress: () => continueWithProfileUpdate(avatarUrl) },
            { text: 'Cancel', onPress: () => setIsSaving(false) }
          ]
        );
        return;
      }

      // Update profile with retry logic for network errors
      const updatedProfile = await retryWithBackoff(async () => {
        return await DatabaseService.updateUserProfile(user.id, {
          display_name: displayName.trim(),
          bio: bio.trim() || null,
          location: location.trim() || null,
          website_url: website.trim() || null,
          avatar_url: avatarUrl || userProfile?.avatar_url,
        });
      });

      if (updatedProfile) {
        Alert.alert('Success', 'Profile updated successfully!', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      } else {
        Alert.alert('Error', 'Failed to update profile. Please check your connection and try again.');
      }
    } catch (error) {
      console.error('Error updating profile in database:', error);
      const errorMessage = getNetworkErrorMessage(error);

      if (isNetworkError(error)) {
        Alert.alert(
          'Connection Error',
          errorMessage,
          [
            { text: 'Retry', onPress: () => continueWithProfileUpdate(avatarUrl) },
            { text: 'Cancel', onPress: () => setIsSaving(false) }
          ]
        );
      } else {
        Alert.alert('Error', `Failed to update profile: ${errorMessage}`, [
          { text: 'OK', onPress: () => setIsSaving(false) },
          { text: 'Diagnostics', onPress: () => { setIsSaving(false); runDiagnostics(); } }
        ]);
      }
    }
  };

  const currentAvatarUri = avatarUri || userProfile?.avatar_url || user?.user_metadata?.avatar_url;

  return (
    <ScrollView style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: 'Update Profile',
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}>
              <ArrowLeft size={24} color={Colors.text} />
            </TouchableOpacity>
          ),
        }} 
      />

      <View style={styles.content}>
        {/* Avatar Section */}
        <Card style={styles.avatarCard}>
          <Text style={styles.sectionTitle}>Profile Picture</Text>
          <View style={styles.avatarContainer}>
            <Image 
              source={{ 
                uri: currentAvatarUri || 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=776&q=80'
              }} 
              style={styles.avatar} 
            />
            <TouchableOpacity onPress={pickImage} style={styles.cameraButton}>
              <Camera size={20} color={Colors.background} />
            </TouchableOpacity>
          </View>
          <Text style={styles.avatarHint}>Tap the camera icon to change your profile picture</Text>
        </Card>

        {/* Profile Information */}
        <Card style={styles.formCard}>
          <Text style={styles.sectionTitle}>Profile Information</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Display Name *</Text>
            <TextInput
              style={styles.textInput}
              value={displayName}
              onChangeText={setDisplayName}
              placeholder="Enter your display name"
              maxLength={50}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Bio</Text>
            <TextInput
              style={[styles.textInput, styles.bioInput]}
              value={bio}
              onChangeText={setBio}
              placeholder="Tell us about yourself..."
              multiline
              numberOfLines={3}
              maxLength={200}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Location</Text>
            <TextInput
              style={styles.textInput}
              value={location}
              onChangeText={setLocation}
              placeholder="Your location"
              maxLength={100}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Website</Text>
            <TextInput
              style={styles.textInput}
              value={website}
              onChangeText={setWebsite}
              placeholder="https://your-website.com"
              keyboardType="url"
              autoCapitalize="none"
              maxLength={200}
            />
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Cancel"
            onPress={() => router.back()}
            variant="secondary"
            style={styles.cancelButton}
            icon={<X size={20} color={Colors.textMuted} />}
          />
          <Button
            title="Save Changes"
            onPress={handleSave}
            loading={isSaving}
            style={styles.saveButton}
            icon={<Check size={20} color={Colors.background} />}
          />
        </View>

        {/* Debug Section - Only show in development */}
        {__DEV__ && (
          <View style={styles.debugSection}>
            <TouchableOpacity
              onPress={() => setShowConfigDebug(true)}
              style={styles.debugButton}
            >
              <Text style={styles.debugButtonText}>🔧 Debug Config</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Debug Modal */}
      <Modal
        visible={showConfigDebug}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <ConfigDebug onClose={() => setShowConfigDebug(false)} />
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  content: {
    padding: 20,
  },
  avatarCard: {
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
    alignSelf: 'flex-start',
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 12,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  avatarHint: {
    fontSize: 12,
    color: Colors.textMuted,
    textAlign: 'center',
  },
  formCard: {
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: Colors.cardBackground,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.text,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  bioInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
  debugSection: {
    marginTop: 20,
    padding: 16,
    backgroundColor: Colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#fbbf24',
  },
  debugButton: {
    padding: 12,
    backgroundColor: '#fbbf24',
    borderRadius: 6,
    alignItems: 'center',
  },
  debugButtonText: {
    color: '#000',
    fontWeight: '600',
    fontSize: 14,
  },
});
